"""
Управление командами бота в зависимости от роли пользователя
"""
import logging
from typing import Dict, List, Set
from aiogram import Bot
from aiogram.types import BotCommand, BotCommandScopeChat, MenuButtonCommands, MenuButtonDefault


class RoleCommandsManager:
    """Менеджер для установки команд в зависимости от роли пользователя"""
    
    def __init__(self):
        # Кэш установленных команд для пользователей {user_id: role}
        self._user_commands_cache: Dict[int, str] = {}
        
        # Определяем команды для каждой роли
        self._role_commands = {
            "admin": [
                Bot<PERSON><PERSON>mand(command="start", description="🏠 Главное меню"),
                Bo<PERSON><PERSON><PERSON><PERSON>(command="student", description="👨‍🎓 Меню студента"),
                <PERSON><PERSON><PERSON><PERSON><PERSON>(command="admin", description="⚙️ Панель администратора"),
                BotCommand(command="manager", description="📊 Меню менеджера"),
                BotCommand(command="teacher", description="👨‍🏫 Меню преподавателя"),
                BotCommand(command="curator", description="👥 Меню куратора")
            ],
            "manager": [
                BotCommand(command="start", description="🏠 Главное меню"),
                BotCommand(command="manager", description="📊 Меню менеджера")
            ],
            "curator": [
                BotCommand(command="start", description="🏠 Главное меню"),
                BotCommand(command="curator", description="👥 Меню куратора")
            ],
            "teacher": [
                BotCommand(command="start", description="🏠 Главное меню"),
                BotCommand(command="teacher", description="👨‍🏫 Меню преподавателя")
            ],
            "student": [
                BotCommand(command="start", description="🏠 Главное меню"),
                BotCommand(command="student", description="👨‍🎓 Меню студента")
            ],
            "new_user": [
                BotCommand(command="start", description="🏠 Главное меню")
            ]
        }
    
    async def set_commands_for_user(self, bot: Bot, user_id: int, role: str) -> bool:
        """
        Установить команды для конкретного пользователя в зависимости от его роли
        
        Args:
            bot: Экземпляр бота
            user_id: ID пользователя в Telegram
            role: Роль пользователя
            
        Returns:
            bool: True если команды были установлены, False если уже были установлены для этой роли
        """
        # Проверяем, нужно ли обновлять команды
        cached_role = self._user_commands_cache.get(user_id)
        if cached_role == role:
            return False  # Команды уже установлены для этой роли
        
        # Получаем команды для роли
        commands = self._role_commands.get(role, self._role_commands["new_user"])
        
        try:
            # Устанавливаем команды для конкретного пользователя
            await bot.set_my_commands(
                commands=commands,
                scope=BotCommandScopeChat(chat_id=user_id)
            )
            
            # Обновляем кэш
            self._user_commands_cache[user_id] = role
            
            logging.info(f"✅ Команды установлены для пользователя {user_id} с ролью '{role}' ({len(commands)} команд)")
            return True
            
        except Exception as e:
            logging.error(f"❌ Ошибка установки команд для пользователя {user_id}: {e}")
            return False
    
    async def remove_commands_for_user(self, bot: Bot, user_id: int) -> bool:
        """
        Удалить команды для конкретного пользователя
        
        Args:
            bot: Экземпляр бота
            user_id: ID пользователя в Telegram
            
        Returns:
            bool: True если команды были удалены успешно
        """
        try:
            # Удаляем команды для конкретного пользователя
            await bot.delete_my_commands(scope=BotCommandScopeChat(chat_id=user_id))
            
            # Удаляем из кэша
            self._user_commands_cache.pop(user_id, None)
            
            logging.info(f"✅ Команды удалены для пользователя {user_id}")
            return True
            
        except Exception as e:
            logging.error(f"❌ Ошибка удаления команд для пользователя {user_id}: {e}")
            return False
    
    def get_commands_for_role(self, role: str) -> List[BotCommand]:
        """
        Получить список команд для роли
        
        Args:
            role: Роль пользователя
            
        Returns:
            List[BotCommand]: Список команд для роли
        """
        return self._role_commands.get(role, self._role_commands["new_user"])
    
    def clear_cache(self):
        """Очистить кэш установленных команд"""
        self._user_commands_cache.clear()
        logging.info("🗑️ Кэш команд очищен")
    
    def get_cache_info(self) -> Dict[str, int]:
        """
        Получить информацию о кэше команд
        
        Returns:
            Dict[str, int]: Статистика по ролям в кэше
        """
        role_counts = {}
        for role in self._user_commands_cache.values():
            role_counts[role] = role_counts.get(role, 0) + 1
        return role_counts


# Глобальный экземпляр менеджера команд
role_commands_manager = RoleCommandsManager()
