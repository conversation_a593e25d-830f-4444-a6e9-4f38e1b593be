"""
Управление постоянными клавиатурами (ReplyKeyboardMarkup) в зависимости от роли пользователя
"""
import logging
from typing import Dict
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton, ReplyKeyboardRemove


class RoleKeyboardsManager:
    """Менеджер для установки постоянных клавиатур в зависимости от роли пользователя"""
    
    def __init__(self):
        # Кэш установленных клавиатур для пользователей {user_id: role}
        self._user_keyboard_cache: Dict[int, str] = {}
        
        # Определяем клавиатуры для каждой роли
        self._role_keyboards = {
            "admin": ReplyKeyboardMarkup(
                keyboard=[
                    [KeyboardButton(text="админ")],
                    [KeyboardButton(text="менеджер"), KeyboardButton(text="преподаватель")],
                    [KeyboardButton(text="куратор"), KeyboardButton(text="ученик")]
                ],
                resize_keyboard=True,
                persistent=True
            ),
            "manager": ReplyKeyboardMarkup(
                keyboard=[
                    [KeyboardButton(text="менеджер")]
                ],
                resize_keyboard=True,
                persistent=True
            ),
            "curator": ReplyKeyboardMarkup(
                keyboard=[
                    [KeyboardButton(text="куратор")]
                ],
                resize_keyboard=True,
                persistent=True
            ),
            "teacher": ReplyKeyboardMarkup(
                keyboard=[
                    [KeyboardButton(text="преподаватель")]
                ],
                resize_keyboard=True,
                persistent=True
            ),
            "student": ReplyKeyboardMarkup(
                keyboard=[
                    [KeyboardButton(text="ученик")]
                ],
                resize_keyboard=True,
                persistent=True
            ),
            "new_user": ReplyKeyboardMarkup(
                keyboard=[
                    [KeyboardButton(text="старт")]
                ],
                resize_keyboard=True,
                persistent=True
            )
        }
    
    async def set_keyboard_for_user(self, message, role: str) -> bool:
        """
        Установить постоянную клавиатуру для конкретного пользователя в зависимости от его роли
        
        Args:
            message: Объект сообщения для отправки клавиатуры
            role: Роль пользователя
            
        Returns:
            bool: True если клавиатура была установлена, False если уже была установлена для этой роли
        """
        user_id = message.from_user.id
        
        # Проверяем, нужно ли обновлять клавиатуру
        cached_role = self._user_keyboard_cache.get(user_id)
        if cached_role == role:
            return False  # Клавиатура уже установлена для этой роли
        
        # Получаем клавиатуру для роли
        keyboard = self._role_keyboards.get(role, self._role_keyboards["new_user"])
        
        try:
            # Отправляем сообщение с клавиатурой
            role_names = {
                "admin": "администратора",
                "manager": "менеджера", 
                "curator": "куратора",
                "teacher": "преподавателя",
                "student": "студента",
                "new_user": "нового пользователя"
            }
            
            await message.answer(
                f"🎛️ Установлена клавиатура для роли: {role_names.get(role, role)}",
                reply_markup=keyboard
            )
            
            # Обновляем кэш
            self._user_keyboard_cache[user_id] = role
            
            logging.info(f"✅ Клавиатура установлена для пользователя {user_id} с ролью '{role}'")
            return True
            
        except Exception as e:
            logging.error(f"❌ Ошибка установки клавиатуры для пользователя {user_id}: {e}")
            return False
    
    async def remove_keyboard_for_user(self, message) -> bool:
        """
        Удалить постоянную клавиатуру для конкретного пользователя
        
        Args:
            message: Объект сообщения для удаления клавиатуры
            
        Returns:
            bool: True если клавиатура была удалена успешно
        """
        user_id = message.from_user.id
        
        try:
            # Удаляем клавиатуру
            await message.answer(
                "🗑️ Клавиатура удалена",
                reply_markup=ReplyKeyboardRemove()
            )
            
            # Удаляем из кэша
            self._user_keyboard_cache.pop(user_id, None)
            
            logging.info(f"✅ Клавиатура удалена для пользователя {user_id}")
            return True
            
        except Exception as e:
            logging.error(f"❌ Ошибка удаления клавиатуры для пользователя {user_id}: {e}")
            return False
    
    def get_keyboard_for_role(self, role: str) -> ReplyKeyboardMarkup:
        """
        Получить клавиатуру для роли
        
        Args:
            role: Роль пользователя
            
        Returns:
            ReplyKeyboardMarkup: Клавиатура для роли
        """
        return self._role_keyboards.get(role, self._role_keyboards["new_user"])
    
    def clear_cache(self):
        """Очистить кэш установленных клавиатур"""
        self._user_keyboard_cache.clear()
        logging.info("🗑️ Кэш клавиатур очищен")
    
    def get_cache_info(self) -> Dict[str, int]:
        """
        Получить информацию о кэше клавиатур
        
        Returns:
            Dict[str, int]: Статистика по ролям в кэше
        """
        role_counts = {}
        for role in self._user_keyboard_cache.values():
            role_counts[role] = role_counts.get(role, 0) + 1
        return role_counts


# Глобальный экземпляр менеджера клавиатур
role_keyboards_manager = RoleKeyboardsManager()
