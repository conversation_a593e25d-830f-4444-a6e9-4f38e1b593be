"""
Миграция уроков для привязки к курсам
"""
import asyncio
from database import LessonRepository, CourseRepository, SubjectRepository
from database.database import get_db_session
from database.models import Lesson, Course, Subject
from sqlalchemy import select, update


async def migrate_lessons_to_courses():
    """Миграция существующих уроков для привязки к курсам"""
    print("🔄 Начинаем миграцию уроков для привязки к курсам...")
    
    async with get_db_session() as session:
        # Получаем все существующие уроки без course_id
        result = await session.execute(
            select(Lesson).where(Lesson.course_id.is_(None))
        )
        lessons_without_course = result.scalars().all()
        
        if not lessons_without_course:
            print("✅ Все уроки уже привязаны к курсам")
            return
            
        print(f"📝 Найдено {len(lessons_without_course)} уроков без привязки к курсу")
        
        # Получаем все курсы
        courses = await CourseRepository.get_all()
        if not courses:
            print("❌ Не найдено ни одного курса для миграции")
            return
            
        # Для каждого урока находим подходящий курс
        migrated_count = 0
        for lesson in lessons_without_course:
            # Получаем предмет урока
            subject_result = await session.execute(
                select(Subject).where(Subject.id == lesson.subject_id)
            )
            subject = subject_result.scalar_one_or_none()
            
            if not subject:
                print(f"⚠️ Предмет с ID {lesson.subject_id} не найден для урока '{lesson.name}'")
                continue
                
            # Ищем курс, который содержит этот предмет
            suitable_course = None
            for course in courses:
                if subject in course.subjects:
                    suitable_course = course
                    break
                    
            if not suitable_course:
                # Если не найден подходящий курс, привязываем к первому доступному
                suitable_course = courses[0]
                print(f"⚠️ Для урока '{lesson.name}' (предмет: {subject.name}) не найден подходящий курс. Привязываем к курсу '{suitable_course.name}'")
            
            # Обновляем урок
            await session.execute(
                update(Lesson)
                .where(Lesson.id == lesson.id)
                .values(course_id=suitable_course.id)
            )
            
            migrated_count += 1
            print(f"✅ Урок '{lesson.name}' привязан к курсу '{suitable_course.name}'")
            
        await session.commit()
        print(f"🎉 Миграция завершена! Обновлено {migrated_count} уроков")


async def verify_migration():
    """Проверка результатов миграции"""
    print("\n🔍 Проверяем результаты миграции...")
    
    async with get_db_session() as session:
        # Проверяем, остались ли уроки без course_id
        result = await session.execute(
            select(Lesson).where(Lesson.course_id.is_(None))
        )
        lessons_without_course = result.scalars().all()
        
        if lessons_without_course:
            print(f"❌ Остались уроки без привязки к курсу: {len(lessons_without_course)}")
            for lesson in lessons_without_course:
                print(f"   - {lesson.name} (ID: {lesson.id})")
        else:
            print("✅ Все уроки успешно привязаны к курсам")
            
        # Показываем статистику по курсам
        result = await session.execute(
            select(Lesson, Course, Subject)
            .join(Course, Lesson.course_id == Course.id)
            .join(Subject, Lesson.subject_id == Subject.id)
        )
        lessons_data = result.all()
        
        course_stats = {}
        for lesson, course, subject in lessons_data:
            if course.name not in course_stats:
                course_stats[course.name] = {}
            if subject.name not in course_stats[course.name]:
                course_stats[course.name][subject.name] = 0
            course_stats[course.name][subject.name] += 1
            
        print("\n📊 Статистика уроков по курсам:")
        for course_name, subjects in course_stats.items():
            print(f"  📚 {course_name}:")
            for subject_name, lesson_count in subjects.items():
                print(f"    📖 {subject_name}: {lesson_count} уроков")


async def main():
    """Основная функция миграции"""
    try:
        await migrate_lessons_to_courses()
        await verify_migration()
    except Exception as e:
        print(f"❌ Ошибка при миграции: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
