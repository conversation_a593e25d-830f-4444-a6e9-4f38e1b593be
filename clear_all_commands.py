"""
Скрипт для полной очистки всех команд бота
"""
import asyncio
from aiogram import Bo<PERSON>
from aiogram.types import BotCommand, BotCommandScopeDefault, BotCommandScopeAllPrivateChats, BotCommandScopeAllGroupChats, BotCommandScopeAllChatAdministrators, BotCommandScopeChat
from utils.config import TOKEN

async def clear_all_commands():
    """Очистить все команды во всех scope"""
    bot = Bot(token=TOKEN)
    
    try:
        # Очищаем команды для всех возможных scope
        scopes = [
            BotCommandScopeDefault(),
            BotCommandScopeAllPrivateChats(),
            BotCommandScopeAllGroupChats(),
            BotCommandScopeAllChatAdministrators()
        ]
        
        for scope in scopes:
            try:
                await bot.delete_my_commands(scope=scope)
                print(f"🗑️ Команды удалены для scope: {scope.__class__.__name__}")
            except Exception as e:
                print(f"⚠️ Не удалось удалить команды для {scope.__class__.__name__}: {e}")
        
        # Также очищаем команды по умолчанию (без scope)
        await bot.delete_my_commands()
        print("🗑️ Команды по умолчанию удалены")

        # Очищаем команды для конкретных пользователей (админов)
        admin_ids = [955518340]  # ID админов из проекта
        for admin_id in admin_ids:
            try:
                await bot.delete_my_commands(scope=BotCommandScopeChat(chat_id=admin_id))
                print(f"🗑️ Команды удалены для пользователя {admin_id}")
            except Exception as e:
                print(f"⚠️ Не удалось удалить команды для пользователя {admin_id}: {e}")
        
        # Устанавливаем только команду /start
        commands = [
            BotCommand(command="start", description="🏠 Главное меню")
        ]
        
        await bot.set_my_commands(commands)
        print("✅ Установлена только команда /start")
        
        # Проверяем, что установилось
        current_commands = await bot.get_my_commands()
        print(f"📋 Текущие команды: {[f'/{cmd.command} - {cmd.description}' for cmd in current_commands]}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    finally:
        await bot.session.close()

if __name__ == "__main__":
    asyncio.run(clear_all_commands())
