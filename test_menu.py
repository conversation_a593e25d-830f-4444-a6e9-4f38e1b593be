"""
Тестовый скрипт для проверки системы кнопок меню по ролям
"""
from utils.menu_buttons import menu_buttons_manager

def test_menu_buttons():
    """Тестирование кнопок меню для разных ролей"""
    
    print("🧪 Тестирование системы кнопок меню по ролям\n")
    
    roles = ["admin", "manager", "curator", "teacher", "student", "new_user"]
    
    for role in roles:
        commands = menu_buttons_manager.get_commands_for_role(role)
        print(f"📋 Роль '{role}' ({len(commands)} команд):")
        for cmd in commands:
            print(f"   /{cmd.command} - {cmd.description}")
        print()
    
    print("✅ Тест завершен успешно!")

if __name__ == "__main__":
    test_menu_buttons()
