"""
Скрипт для очистки команд бота
"""
import asyncio
from aiogram import Bo<PERSON>
from aiogram.types import BotCommand
from utils.config import TOKEN

async def clear_and_set_commands():
    """Очистить старые команды и установить только /start"""
    bot = Bot(token=TOKEN)
    
    try:
        # Удаляем все существующие команды
        await bot.delete_my_commands()
        print("🗑️ Все старые команды удалены")
        
        # Устанавливаем только команду /start
        commands = [
            BotCommand(command="start", description="🏠 Главное меню")
        ]
        
        await bot.set_my_commands(commands)
        print("✅ Установлена только команда /start")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    finally:
        await bot.session.close()

if __name__ == "__main__":
    asyncio.run(clear_and_set_commands())
