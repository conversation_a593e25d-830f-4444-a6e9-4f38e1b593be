"""
Тестовый скрипт для проверки системы команд по ролям
"""
from utils.role_commands import role_commands_manager

def test_role_commands():
    """Тестирование команд для разных ролей"""
    
    print("🧪 Тестирование системы команд по ролям\n")
    
    roles = ["admin", "manager", "curator", "teacher", "student", "new_user"]
    
    for role in roles:
        commands = role_commands_manager.get_commands_for_role(role)
        print(f"📋 Роль '{role}' ({len(commands)} команд):")
        for cmd in commands:
            print(f"   /{cmd.command} - {cmd.description}")
        print()
    
    print("✅ Тест завершен успешно!")

if __name__ == "__main__":
    test_role_commands()
